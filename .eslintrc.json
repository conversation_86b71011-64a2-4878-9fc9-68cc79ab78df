{"extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:jsx-a11y/recommended", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["@typescript-eslint", "react", "react-hooks", "jsx-a11y", "prettier"], "rules": {"prettier/prettier": "error", "react/react-in-jsx-scope": "off", "react/prop-types": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "jsx-a11y/anchor-is-valid": "off", "jsx-a11y/label-has-associated-control": "off", "react/no-unescaped-entities": "off", "prefer-const": "error", "no-var": "error"}, "settings": {"react": {"version": "detect"}}, "env": {"browser": true, "es2021": true, "node": true}}